import { Entity, PrimaryColumn, Column } from 'typeorm';

@Entity({ name: 'employees', synchronize: false })
export class EmployeeEntity {
  @PrimaryColumn({ type: 'uniqueidentifier' })
  uuid: string;

  @Column({ name: 'int_employeeglobalid' })
  globalId: number;

  @Column({ name: 'str_email' })
  email: string;

  @Column({ name: 'str_firstname' })
  firstname: string;

  @Column({ name: 'str_lastname' })
  lastname: string;

  @Column({ name: 'str_positiontitle' })
  positionTitle: string;
}
